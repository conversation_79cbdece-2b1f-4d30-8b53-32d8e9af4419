import { Metadata } from "next";
import { WorkspacePageWrapper } from "@/src/features/auth/components/workspace-page-wrapper";

export const metadata: Metadata = {
  title: "Home | Centaly"
};

interface PageProps {
  params: Promise<{ slug: string }>;
}

export default async function WorkspaceHomePage({ params }: PageProps) {
  const { slug } = await params;

  return (
    <WorkspacePageWrapper slug={slug}>
      <div className="flex flex-col p-6">
        <h1>Home</h1>
      </div>
    </WorkspacePageWrapper>
  );
}
