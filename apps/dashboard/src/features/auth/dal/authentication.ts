import { AuthDAL, type WorkspaceAccessResult } from "./auth-dal";

/**
 * Require workspace access - validates that the authenticated user has access to the specified workspace
 * This function is used by pages to ensure users can only access workspaces they belong to
 * 
 * @param slug - The workspace slug from the URL parameter
 * @returns Promise<WorkspaceAccessResult> - Contains session, organizations, and current workspace data
 * @throws Redirects to sign-in if not authenticated
 * @throws Redirects to verification if email not verified  
 * @throws Redirects to onboarding if not completed
 * @throws Returns 404 if user doesn't have access to the workspace
 * 
 * Usage:
 * ```tsx
 * const { session, organizations, currentWorkspace } = await requireWorkspaceAccess(slug);
 * ```
 */
export async function requireWorkspaceAccess(slug: string): Promise<WorkspaceAccessResult> {
  return AuthDAL.requireWorkspaceAccess(slug);
}

/**
 * Check workspace access without throwing - useful for conditional logic
 * 
 * @param slug - The workspace slug from the URL parameter
 * @returns Promise<AuthorizationResult & { workspaceData?: WorkspaceAccessResult }>
 */
export async function checkWorkspaceAccess(slug: string) {
  return AuthDAL.checkWorkspacePageAccess(slug);
}
