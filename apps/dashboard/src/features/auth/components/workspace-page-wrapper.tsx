import { ReactNode } from "react";
import { AuthDAL } from "@/src/features/auth/dal/auth-dal";
import { redirect, notFound } from "next/navigation";

interface WorkspacePageWrapperProps {
  children: ReactNode;
  slug: string;
}

/**
 * Route Protection Component for Workspace Pages
 *
 * This component implements the Data Access Layer (DAL) pattern for route-level
 * authorization as recommended in the Next.js documentation.
 *
 * It protects workspace pages by:
 * 1. Checking if the user is authenticated
 * 2. If not authenticated, redirecting to sign-in
 * 3. If email not verified, redirecting to verification
 * 4. If onboarding not complete, redirecting to appropriate onboarding step
 * 5. Validating that the user has access to the specified workspace
 * 6. If user doesn't have access to workspace, showing 404 error
 * 7. If all checks pass, renders the page content
 *
 * Access Requirements:
 * - User must be authenticated
 * - User must have verified email
 * - User must have completed onboarding
 * - User must be a member of the workspace identified by the slug
 *
 * Error Handling:
 * - Unauthenticated users → redirect to /sign-in
 * - Unverified email → redirect to /verify
 * - Incomplete onboarding → redirect to appropriate onboarding step
 * - No workspace access → 404 error (prevents workspace enumeration)
 *
 * Usage:
 * ```tsx
 * export default async function WorkspacePage({ params }: PageProps) {
 *   const { slug } = await params;
 *   
 *   return (
 *     <WorkspacePageWrapper slug={slug}>
 *       <div>Your workspace page content</div>
 *     </WorkspacePageWrapper>
 *   );
 * }
 * ```
 */
export async function WorkspacePageWrapper({ 
  children, 
  slug 
}: WorkspacePageWrapperProps) {
  // Use the DAL to check if user has access to this workspace
  const authResult = await AuthDAL.checkWorkspacePageAccess(slug);

  // If user should be redirected, perform the redirect
  if (authResult.shouldRedirect && authResult.redirectTo) {
    redirect(authResult.redirectTo);
  }

  // If user doesn't have access to workspace, show 404
  // This prevents workspace enumeration attacks
  if (!authResult.workspaceData) {
    notFound();
  }

  // If user has proper access, render the page content
  return <>{children}</>;
}
